<?php
// Configure session directory before starting session
$session_dir = __DIR__ . '/sessions';
if (!file_exists($session_dir)) {
    @mkdir($session_dir, 0755, true);
}

// Set session configuration
ini_set('session.save_path', $session_dir);
ini_set('session.gc_probability', 1);
ini_set('session.gc_maxlifetime', 86400);
ini_set('session.cookie_lifetime', 86400);
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_httponly', 1);

// Start session with error handling
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
} catch (Exception $e) {
    // If session fails, create a new session directory and try again
    $session_dir = __DIR__ . '/sessions_backup';
    if (!file_exists($session_dir)) {
        @mkdir($session_dir, 0755, true);
    }
    ini_set('session.save_path', $session_dir);
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to modern login page
    header('Location: login_modern.php');
    exit;
} else {
    // Redirect to simplified dashboard
    header('Location: dashboard_simple.php');
    exit;
}
?>

