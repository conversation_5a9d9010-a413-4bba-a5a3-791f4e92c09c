<?php
// Test simple session fix
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Simple Session Fix Test</h1>";

// Include the simple session fix
require_once 'simple_session_fix.php';

echo "<h2>Session Status:</h2>";
switch (session_status()) {
    case PHP_SESSION_DISABLED:
        echo "<p style='color: red;'>❌ Sessions are disabled</p>";
        break;
    case PHP_SESSION_NONE:
        echo "<p style='color: orange;'>⚠️ Sessions are enabled, but none exists</p>";
        break;
    case PHP_SESSION_ACTIVE:
        echo "<p style='color: green;'>✅ Sessions are enabled and active</p>";
        break;
}

if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>Session Save Path:</strong> " . session_save_path() . "</p>";
    
    $save_path = session_save_path();
    echo "<p><strong>Directory Exists:</strong> " . (file_exists($save_path) ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p><strong>Directory Writable:</strong> " . (is_writable($save_path) ? '✅ Yes' : '❌ No') . "</p>";
    
    // Test session variables
    if (!isset($_SESSION['test_counter'])) {
        $_SESSION['test_counter'] = 1;
        $_SESSION['first_visit'] = date('Y-m-d H:i:s');
    } else {
        $_SESSION['test_counter']++;
    }
    
    echo "<h2>Session Test:</h2>";
    echo "<p><strong>Visit Counter:</strong> " . $_SESSION['test_counter'] . "</p>";
    echo "<p><strong>First Visit:</strong> " . $_SESSION['first_visit'] . "</p>";
    echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    
    echo "<p><em>Refresh this page to see the counter increment.</em></p>";
}

echo "<h2>Actions:</h2>";
echo "<p><a href='index.php'>Test Index Page</a></p>";
echo "<p><a href='test_simple_session.php'>Refresh This Page</a></p>";

if (isset($_GET['clear']) && session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
    echo "<p style='color: green;'><strong>✅ Session cleared!</strong></p>";
    echo "<p><a href='test_simple_session.php'>Start New Session</a></p>";
} else {
    echo "<p><a href='test_simple_session.php?clear=1'>Clear Session</a></p>";
}

echo "<h2>PHP Info:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
?>
