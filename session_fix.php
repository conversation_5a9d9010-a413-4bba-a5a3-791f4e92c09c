<?php
/**
 * Session Fix for cPanel Hosting
 * This file handles session configuration and initialization
 * to prevent session directory permission errors
 */

// Prevent multiple inclusions
if (defined('SESSION_FIX_LOADED')) {
    return;
}
define('SESSION_FIX_LOADED', true);

// Function to create and configure session directory
function setup_session_directory() {
    // Try multiple session directory options
    $session_dirs = [
        __DIR__ . '/sessions',
        __DIR__ . '/tmp/sessions',
        sys_get_temp_dir() . '/php_sessions_' . md5(__DIR__),
        '/tmp/php_sessions_' . md5(__DIR__)
    ];

    foreach ($session_dirs as $session_dir) {
        // Try to create directory if it doesn't exist
        if (!file_exists($session_dir)) {
            if (@mkdir($session_dir, 0755, true)) {
                // Directory created successfully
                if (is_writable($session_dir)) {
                    return $session_dir;
                }
            }
        } elseif (is_writable($session_dir)) {
            // Directory exists and is writable
            return $session_dir;
        }
    }

    // If all else fails, use system temp directory
    return sys_get_temp_dir();
}

// Configure session directory
$session_dir = setup_session_directory();
ini_set('session.save_path', $session_dir);

// Configure session settings for cPanel compatibility
ini_set('session.gc_probability', 1);
ini_set('session.gc_divisor', 100);
ini_set('session.gc_maxlifetime', 86400); // 24 hours
ini_set('session.cookie_lifetime', 86400); // 24 hours
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS
ini_set('session.use_trans_sid', 0);
ini_set('session.cache_limiter', 'nocache');

// Start session with error handling
function safe_session_start() {
    if (session_status() === PHP_SESSION_NONE) {
        try {
            return session_start();
        } catch (Exception $e) {
            // Log error but don't stop execution
            error_log("Session start error: " . $e->getMessage());

            // Try with a different session directory
            $backup_dir = sys_get_temp_dir() . '/backup_sessions_' . md5(__DIR__);
            if (!file_exists($backup_dir)) {
                @mkdir($backup_dir, 0755, true);
            }
            ini_set('session.save_path', $backup_dir);

            try {
                return session_start();
            } catch (Exception $e2) {
                error_log("Backup session start error: " . $e2->getMessage());
                return false;
            }
        }
    }
    return true;
}

// Initialize session
safe_session_start();
?>
