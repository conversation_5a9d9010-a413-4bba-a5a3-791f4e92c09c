<?php
// Test session fix for cPanel hosting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Session Fix Test</h1>";

// Include the session fix
require_once 'session_fix.php';

echo "<h2>Session Information:</h2>";
echo "<p><strong>Session Status:</strong> ";
switch (session_status()) {
    case PHP_SESSION_DISABLED:
        echo "Sessions are disabled";
        break;
    case PHP_SESSION_NONE:
        echo "Sessions are enabled, but none exists";
        break;
    case PHP_SESSION_ACTIVE:
        echo "Sessions are enabled, and one exists";
        break;
}
echo "</p>";

echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Save Path:</strong> " . session_save_path() . "</p>";
echo "<p><strong>Session Directory Exists:</strong> " . (file_exists(session_save_path()) ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Session Directory Writable:</strong> " . (is_writable(session_save_path()) ? 'Yes' : 'No') . "</p>";

// Test session variables
if (!isset($_SESSION['test_counter'])) {
    $_SESSION['test_counter'] = 1;
} else {
    $_SESSION['test_counter']++;
}

echo "<h2>Session Test:</h2>";
echo "<p><strong>Test Counter:</strong> " . $_SESSION['test_counter'] . "</p>";
echo "<p><em>Refresh this page to see the counter increment.</em></p>";

// Test session data persistence
$_SESSION['test_time'] = date('Y-m-d H:i:s');
echo "<p><strong>Session Time:</strong> " . $_SESSION['test_time'] . "</p>";

echo "<h2>PHP Configuration:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

echo "<h2>Session Configuration:</h2>";
echo "<p><strong>session.save_path:</strong> " . ini_get('session.save_path') . "</p>";
echo "<p><strong>session.gc_maxlifetime:</strong> " . ini_get('session.gc_maxlifetime') . "</p>";
echo "<p><strong>session.cookie_lifetime:</strong> " . ini_get('session.cookie_lifetime') . "</p>";
echo "<p><strong>session.use_cookies:</strong> " . ini_get('session.use_cookies') . "</p>";
echo "<p><strong>session.cookie_httponly:</strong> " . ini_get('session.cookie_httponly') . "</p>";

echo "<h2>Actions:</h2>";
echo "<p><a href='index.php'>Test Index Page</a></p>";
echo "<p><a href='login_modern.php'>Go to Login</a></p>";
echo "<p><a href='test_session_fix.php'>Refresh This Page</a></p>";

// Clear session test
if (isset($_GET['clear'])) {
    session_destroy();
    echo "<p style='color: green;'><strong>Session cleared!</strong></p>";
    echo "<p><a href='test_session_fix.php'>Start New Session</a></p>";
} else {
    echo "<p><a href='test_session_fix.php?clear=1'>Clear Session</a></p>";
}
?>
