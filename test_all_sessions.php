<?php
// Comprehensive test for all session configurations
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Comprehensive Session Test</h1>";
echo "<p>Testing all session configurations to ensure no warnings...</p>";

$test_results = [];

// Test 1: Simple session fix
echo "<h2>Test 1: Simple Session Fix</h2>";
ob_start();
require_once 'simple_session_fix.php';
$output = ob_get_clean();

if (empty($output)) {
    echo "<p style='color: green;'>✅ simple_session_fix.php - No warnings</p>";
    $test_results['simple_session_fix'] = 'PASS';
} else {
    echo "<p style='color: red;'>❌ simple_session_fix.php - Has warnings:</p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    $test_results['simple_session_fix'] = 'FAIL';
}

// Test 2: Check session functionality
echo "<h2>Test 2: Session Functionality</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p style='color: green;'>✅ Session is active</p>";
    
    // Test session variables
    $_SESSION['test_var'] = 'test_value_' . time();
    if (isset($_SESSION['test_var'])) {
        echo "<p style='color: green;'>✅ Session variables work</p>";
        $test_results['session_functionality'] = 'PASS';
    } else {
        echo "<p style='color: red;'>❌ Session variables don't work</p>";
        $test_results['session_functionality'] = 'FAIL';
    }
} else {
    echo "<p style='color: red;'>❌ Session is not active</p>";
    $test_results['session_functionality'] = 'FAIL';
}

// Test 3: Test including db_connect.php
echo "<h2>Test 3: Database Connection with Session</h2>";
ob_start();
require_once 'db_connect.php';
$output = ob_get_clean();

if (empty($output)) {
    echo "<p style='color: green;'>✅ db_connect.php - No warnings</p>";
    $test_results['db_connect'] = 'PASS';
} else {
    echo "<p style='color: red;'>❌ db_connect.php - Has warnings:</p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    $test_results['db_connect'] = 'FAIL';
}

// Test 4: Test including check_session.php
echo "<h2>Test 4: Check Session Include</h2>";
ob_start();
// Temporarily set a user_id to avoid redirect
$_SESSION['user_id'] = 999;
require_once 'check_session.php';
$output = ob_get_clean();

if (empty($output)) {
    echo "<p style='color: green;'>✅ check_session.php - No warnings</p>";
    $test_results['check_session'] = 'PASS';
} else {
    echo "<p style='color: red;'>❌ check_session.php - Has warnings:</p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    $test_results['check_session'] = 'FAIL';
}

// Clean up test session variable
unset($_SESSION['user_id']);

// Test 5: Session directory and permissions
echo "<h2>Test 5: Session Directory</h2>";
$session_path = session_save_path();
echo "<p><strong>Session Path:</strong> " . $session_path . "</p>";

if (file_exists($session_path)) {
    echo "<p style='color: green;'>✅ Session directory exists</p>";
    if (is_writable($session_path)) {
        echo "<p style='color: green;'>✅ Session directory is writable</p>";
        $test_results['session_directory'] = 'PASS';
    } else {
        echo "<p style='color: red;'>❌ Session directory is not writable</p>";
        $test_results['session_directory'] = 'FAIL';
    }
} else {
    echo "<p style='color: red;'>❌ Session directory does not exist</p>";
    $test_results['session_directory'] = 'FAIL';
}

// Test Summary
echo "<h2>Test Summary</h2>";
$total_tests = count($test_results);
$passed_tests = count(array_filter($test_results, function($result) { return $result === 'PASS'; }));

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Test</th><th>Result</th></tr>";
foreach ($test_results as $test => $result) {
    $color = $result === 'PASS' ? 'green' : 'red';
    $icon = $result === 'PASS' ? '✅' : '❌';
    echo "<tr><td>" . ucfirst(str_replace('_', ' ', $test)) . "</td><td style='color: $color;'>$icon $result</td></tr>";
}
echo "</table>";

echo "<h3>Overall Result:</h3>";
if ($passed_tests === $total_tests) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 ALL TESTS PASSED! Session configuration is working correctly.</p>";
} else {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>⚠️ Some tests failed. Please check the issues above.</p>";
}

echo "<p><strong>Passed:</strong> $passed_tests / $total_tests</p>";

echo "<h2>Session Information:</h2>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
echo "<p><strong>Session Save Path:</strong> " . session_save_path() . "</p>";

echo "<h2>Actions:</h2>";
echo "<p><a href='index.php'>Test Index Page</a></p>";
echo "<p><a href='login_modern.php'>Test Login Page</a></p>";
echo "<p><a href='test_all_sessions.php'>Refresh This Test</a></p>";
?>
