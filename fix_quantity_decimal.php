<?php
// Habilitar exibição de erros para depuração
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir conexão com o banco de dados
require_once 'db_connect.php';

echo "<h1>Correção do Campo Quantity para Suportar Valores Decimais</h1>";

// Verificar se as tabelas existem
$tables_to_fix = ['invoice_items', 'quote_items'];
$success = true;

foreach ($tables_to_fix as $table) {
    echo "<h2>Verificando tabela: $table</h2>";
    
    // Verificar se a tabela existe
    $result = db_query("SHOW TABLES LIKE '$table'");
    if (db_num_rows($result) == 0) {
        echo "<p style='color: red;'>Erro: A tabela '$table' não existe.</p>";
        $success = false;
        continue;
    }
    
    // Verificar a estrutura atual do campo quantity
    $result = db_query("SHOW COLUMNS FROM $table LIKE 'quantity'");
    if (db_num_rows($result) > 0) {
        $column_info = db_fetch_assoc($result);
        echo "<p>Tipo atual do campo quantity: <strong>" . $column_info['Type'] . "</strong></p>";
        
        // Verificar se já é DECIMAL
        if (strpos(strtoupper($column_info['Type']), 'DECIMAL') !== false) {
            echo "<p style='color: green;'>O campo quantity já está configurado como DECIMAL. Nenhuma alteração necessária.</p>";
            continue;
        }
        
        // Alterar o tipo do campo para DECIMAL
        echo "<p>Alterando o tipo do campo quantity para DECIMAL(10,3)...</p>";
        
        $alter_query = "ALTER TABLE $table MODIFY COLUMN quantity DECIMAL(10,3) NOT NULL";
        
        if (db_query($alter_query)) {
            echo "<p style='color: green;'>Campo quantity alterado com sucesso para DECIMAL(10,3)!</p>";
            
            // Verificar a alteração
            $result = db_query("SHOW COLUMNS FROM $table LIKE 'quantity'");
            if (db_num_rows($result) > 0) {
                $column_info = db_fetch_assoc($result);
                echo "<p>Novo tipo do campo quantity: <strong>" . $column_info['Type'] . "</strong></p>";
            }
        } else {
            echo "<p style='color: red;'>Erro ao alterar o campo quantity: " . db_error() . "</p>";
            $success = false;
        }
    } else {
        echo "<p style='color: red;'>Erro: O campo 'quantity' não existe na tabela '$table'.</p>";
        $success = false;
    }
    
    echo "<hr>";
}

if ($success) {
    echo "<h2 style='color: green;'>Correção concluída com sucesso!</h2>";
    echo "<p>Agora você pode usar valores decimais para quantidade, como 5.33 libras de freon.</p>";
    echo "<p><strong>Exemplo:</strong> 5.33, 2.5, 10.75, etc.</p>";
} else {
    echo "<h2 style='color: red;'>Houve erros durante a correção.</h2>";
    echo "<p>Por favor, verifique os erros acima e tente novamente.</p>";
}

echo "<p><a href='invoice_create.php'>Testar criação de invoice</a> | <a href='quote_create.php'>Testar criação de quote</a></p>";

// Fechar conexão com o banco de dados
db_close();
?>
