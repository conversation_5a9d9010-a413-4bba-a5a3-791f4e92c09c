<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$customer_id = $quote_id = 0;
$invoice_date = date('Y-m-d');
$due_date = date('Y-m-d', strtotime('+5 days'));
$notes = '';
$subtotal = $tax_rate = $tax_amount = $discount_amount = $total = 0;
$error = $success = '';
$items = [];

// Check if creating from quote
if (isset($_GET['quote_id'])) {
    $quote_id = intval($_GET['quote_id']);

    // Get quote data
    $result = db_query("SELECT * FROM quotes WHERE id = $quote_id");
    if ($result && db_num_rows($result) > 0) {
        $quote = db_fetch_assoc($result);

        $customer_id = $quote['customer_id'];
        $subtotal = $quote['subtotal'];
        $tax_rate = $quote['tax_rate'];
        $tax_amount = $quote['tax_amount'];
        $discount_amount = $quote['discount_amount'];
        $total = $quote['total'];
        $notes = $quote['notes'];

        // Get quote items
        $result = db_query("
            SELECT qi.*, i.name as item_name
            FROM quote_items qi
            JOIN inventory i ON qi.inventory_id = i.id
            WHERE qi.quote_id = $quote_id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        }
    }
}

// Get all customers for dropdown
$customers = [];
$result = db_query("SELECT id, name FROM customers ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $customers = db_fetch_all($result);
}

// Get all inventory items for dropdown
$inventory = [];
$result = db_query("SELECT id, name, price, sku FROM inventory ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $inventory = db_fetch_all($result);
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $customer_id = intval(isset($_POST['customer_id']) ? $_POST['customer_id'] : 0);
    $quote_id = intval(isset($_POST['quote_id']) ? $_POST['quote_id'] : 0);
    $invoice_date = isset($_POST['invoice_date']) ? $_POST['invoice_date'] : date('Y-m-d');
    $due_date = isset($_POST['due_date']) ? $_POST['due_date'] : '';
    $subtotal = floatval(isset($_POST['subtotal']) ? $_POST['subtotal'] : 0);
    $tax_rate = floatval(isset($_POST['tax_rate']) ? $_POST['tax_rate'] : 0);
    $tax_amount = floatval(isset($_POST['tax_amount']) ? $_POST['tax_amount'] : 0);
    $discount_amount = floatval(isset($_POST['discount_amount']) ? $_POST['discount_amount'] : 0);
    $total = floatval(isset($_POST['total']) ? $_POST['total'] : 0);
    $notes = isset($_POST['notes']) ? $_POST['notes'] : '';

    // Get items
    $item_ids = isset($_POST['item_id']) ? $_POST['item_id'] : [];
    $item_descriptions = isset($_POST['item_description']) ? $_POST['item_description'] : [];
    $item_quantities = isset($_POST['item_quantity']) ? $_POST['item_quantity'] : [];
    $item_prices = isset($_POST['item_price']) ? $_POST['item_price'] : [];
    $item_subtotals = isset($_POST['item_subtotal']) ? $_POST['item_subtotal'] : [];

    // Build items array
    for ($i = 0; $i < count($item_ids); $i++) {
        if (!empty($item_ids[$i])) {
            $items[] = [
                'id' => intval($item_ids[$i]),
                'description' => $item_descriptions[$i],
                'quantity' => floatval($item_quantities[$i]),
                'price' => floatval($item_prices[$i]),
                'subtotal' => floatval($item_subtotals[$i])
            ];
        }
    }

    // Validate form data
    if ($customer_id <= 0) {
        $error = 'Selecione um cliente.';
    } elseif (empty($invoice_date)) {
        $error = 'A data da fatura é obrigatória.';
    } elseif (empty($items)) {
        $error = 'Adicione pelo menos um item à fatura.';
    } else {
        // We'll use manual transaction management
        db_begin_transaction();
        $transaction_success = true;

        // Generate invoice number
        $year = date('Y');
        $month = date('m');
        $result = db_query("SELECT MAX(CAST(SUBSTRING_INDEX(invoice_number, '-', -1) AS UNSIGNED)) as max_number FROM invoices WHERE invoice_number LIKE 'FAT-$year$month-%'");
        $row = db_fetch_assoc($result);
        $next_number = 1;

        if ($row && isset($row['max_number']) && $row['max_number']) {
            $next_number = $row['max_number'] + 1;
        }

        $invoice_number = sprintf("FAT-%s%s-%03d", $year, $month, $next_number);

        // Escape strings to prevent SQL injection
        $invoice_number = db_escape($invoice_number);
        $invoice_date = db_escape($invoice_date);
        $due_date = db_escape($due_date);
        $notes = db_escape($notes);

        // Insert invoice
        $query = "INSERT INTO invoices (customer_id, quote_id, invoice_number, invoice_date, due_date, subtotal, tax_rate, tax_amount, discount_amount, total, notes, status) "
               . "VALUES ($customer_id, " . ($quote_id > 0 ? $quote_id : "NULL") . ", '$invoice_number', '$invoice_date', " . (!empty($due_date) ? "'$due_date'" : "NULL") . ", $subtotal, $tax_rate, $tax_amount, $discount_amount, $total, '$notes', 'unpaid')";

        if (db_query($query)) {
            $invoice_id = db_insert_id();

            // Insert invoice items
            foreach ($items as $item) {
                $inventory_id = $item['id'];
                $description = db_escape($item['description']);
                $quantity = $item['quantity'];
                $price = $item['price'];
                $subtotal = $item['subtotal'];

                $query = "INSERT INTO invoice_items (invoice_id, inventory_id, description, quantity, price, subtotal) "
                       . "VALUES ($invoice_id, $inventory_id, '$description', $quantity, $price, $subtotal)";

                if (!db_query($query)) {
                    $transaction_success = false;
                    $error = 'Erro ao inserir item da fatura: ' . db_error();
                    break;
                }
            }

            // Update quote status if created from quote
            if ($transaction_success && $quote_id > 0) {
                $query = "UPDATE quotes SET status = 'approved' WHERE id = $quote_id";
                if (!db_query($query)) {
                    $transaction_success = false;
                    $error = 'Erro ao atualizar status do orçamento: ' . db_error();
                }
            }

            if ($transaction_success) {
                db_commit();
                $success = 'Fatura criada com sucesso! Número: ' . $invoice_number;

                // Clear form data
                $customer_id = $quote_id = 0;
                $invoice_date = date('Y-m-d');
                $due_date = date('Y-m-d', strtotime('+30 days'));
                $notes = '';
                $subtotal = $tax_rate = $tax_amount = $discount_amount = $total = 0;
                $items = [];
            } else {
                db_rollback();
            }
        } else {
            db_rollback();
            $error = 'Erro ao criar fatura: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Fatura - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/menu-fix.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/buttons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <!-- Menu lateral removido -->
    <div class="container-fluid">
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Nova Fatura</h1>
                </div>
                <div class="card-body">
                    <form method="POST" action="invoice_create.php" class="needs-validation" id="invoice-form">
                        <?php if ($quote_id > 0): ?>
                            <input type="hidden" name="quote_id" value="<?php echo $quote_id; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="customer_id">Cliente *</label>
                            <select id="customer_id" name="customer_id" required <?php echo ($quote_id > 0) ? 'readonly' : ''; ?>>
                                <option value="">Selecione um cliente</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>" <?php echo ($customer_id == $customer['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="invoice_date">Data da Fatura *</label>
                            <input type="date" id="invoice_date" name="invoice_date" value="<?php echo $invoice_date; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="due_date">Data de Vencimento</label>
                            <input type="date" id="due_date" name="due_date" value="<?php echo $due_date; ?>">
                        </div>

                        <h3>Itens da Fatura</h3>

                        <div class="items-container" id="items-container">
                            <?php if (!empty($items) && $quote_id > 0): ?>
                                <?php foreach ($items as $item): ?>
                                    <div class="item-row">
                                        <div class="form-group">
                                            <label>Produto</label>
                                            <select name="item_id[]" class="item-select" required onchange="updateItemDetails(this)">
                                                <option value="">Selecione um produto</option>
                                                <?php foreach ($inventory as $inv_item): ?>
                                                    <option value="<?php echo $inv_item['id']; ?>"
                                                            data-price="<?php echo $inv_item['price']; ?>"
                                                            <?php echo ($item['inventory_id'] == $inv_item['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($inv_item['name']); ?>
                                                        <?php echo !empty($inv_item['sku']) ? '(' . htmlspecialchars($inv_item['sku']) . ')' : ''; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Descrição</label>
                                            <input type="text" name="item_description[]" class="item-description" value="<?php echo htmlspecialchars($item['description']); ?>">
                                        </div>

                                        <div class="form-group">
                                            <label>Quantidade</label>
                                            <input type="number" name="item_quantity[]" class="item-quantity" step="1" min="1" value="<?php echo $item['quantity']; ?>" required onchange="calculateItemTotal(this)">
                                        </div>

                                        <div class="form-group">
                                            <label>Preço Unitário</label>
                                            <input type="number" name="item_price[]" class="item-price" step="0.01" min="0" value="<?php echo $item['price']; ?>" required onchange="calculateItemTotal(this)">
                                        </div>

                                        <div class="form-group">
                                            <label>Subtotal</label>
                                            <input type="number" name="item_subtotal[]" class="item-subtotal" step="0.01" min="0" value="<?php echo $item['subtotal']; ?>" readonly>
                                        </div>

                                        <button type="button" class="btn btn-danger remove-item" onclick="removeItem(this)">Remover</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <button type="button" class="btn btn-secondary" onclick="addItemRow()">Adicionar Item</button>

                        <div class="totals-section">
                            <div class="totals-row">
                                <span class="totals-label">Subtotal:</span>
                                <input type="number" id="subtotal" name="subtotal" step="0.01" min="0" value="<?php echo $subtotal; ?>" readonly>
                            </div>

                            <div class="totals-row">
                                <span class="totals-label">Taxa (%):</span>
                                <input type="number" id="tax_rate" name="tax_rate" step="0.01" min="0" value="<?php echo $tax_rate; ?>" onchange="updateTaxAmount()">
                            </div>

                            <div class="totals-row">
                                <span class="totals-label">Valor da Taxa:</span>
                                <input type="number" id="tax_amount" name="tax_amount" step="0.01" min="0" value="<?php echo $tax_amount; ?>" readonly>
                            </div>

                            <div class="totals-row">
                                <span class="totals-label">Desconto:</span>
                                <input type="number" id="discount_amount" name="discount_amount" step="0.01" min="0" value="<?php echo $discount_amount; ?>" onchange="calculateTotals()">
                            </div>

                            <div class="totals-row grand-total">
                                <span class="totals-label">Total:</span>
                                <input type="number" id="total" name="total" step="0.01" min="0" value="<?php echo $total; ?>" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">Observações</label>
                            <textarea id="notes" name="notes"><?php echo htmlspecialchars($notes); ?></textarea>
                        </div>

                        <button type="submit" class="btn">Criar Fatura</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <!-- Item template -->
    <template id="item-template">
        <div class="item-row">
            <div class="form-group">
                <label>Produto</label>
                <select name="item_id[]" class="item-select" required onchange="updateItemDetails(this)">
                    <option value="">Selecione um produto</option>
                    <?php foreach ($inventory as $item): ?>
                        <option value="<?php echo $item['id']; ?>" data-price="<?php echo $item['price']; ?>">
                            <?php echo htmlspecialchars($item['name']); ?> <?php echo !empty($item['sku']) ? '(' . htmlspecialchars($item['sku']) . ')' : ''; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label>Descrição</label>
                <input type="text" name="item_description[]" class="item-description">
            </div>

            <div class="form-group">
                <label>Quantidade</label>
                <input type="number" name="item_quantity[]" class="item-quantity" step="0.001" min="0.001" value="1" required onchange="calculateItemTotal(this)" placeholder="Ex: 5.33">
            </div>

            <div class="form-group">
                <label>Preço Unitário</label>
                <input type="number" name="item_price[]" class="item-price" step="0.01" min="0" required onchange="calculateItemTotal(this)">
            </div>

            <div class="form-group">
                <label>Subtotal</label>
                <input type="number" name="item_subtotal[]" class="item-subtotal" step="0.01" min="0" readonly>
            </div>

            <button type="button" class="btn btn-danger remove-item" onclick="removeItem(this)">Remover</button>
        </div>
    </template>

    <script src="js/main.js"></script>
    <script>
        // Initialize items on page load
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotals();
        });
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
